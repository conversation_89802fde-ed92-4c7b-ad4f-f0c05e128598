#!/usr/bin/env python3
"""
Test Script for Trained ASL Model
Tests the trained model on validation data and provides detailed metrics.
"""

import json
import logging
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, random_split
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns
from train_after_processing import WLASLDataset, ASLVideoClassifier

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_trained_model(model_path):
    """Load the trained model from checkpoint."""
    if not Path(model_path).exists():
        logger.error(f"Model file not found: {model_path}")
        return None, None
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint['config']
    vocab = checkpoint['vocab']
    
    # Create model
    model = ASLVideoClassifier(
        num_classes=vocab['num_classes'], 
        dropout_rate=config.get('dropout_rate', 0.5)
    )
    
    # Load state dict
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    logger.info(f"Model loaded successfully from {model_path}")
    logger.info(f"Model trained for {checkpoint['epoch']+1} epochs")
    logger.info(f"Best validation accuracy: {checkpoint['val_acc']:.2f}%")
    
    return model, vocab

def test_model(model, test_loader, vocab, device='cpu'):
    """Test the model and return detailed metrics."""
    model.to(device)
    model.eval()
    
    all_predictions = []
    all_targets = []
    correct = 0
    total = 0
    
    logger.info("Testing model...")
    
    with torch.no_grad():
        for data, targets in test_loader:
            data, targets = data.to(device), targets.to(device)
            outputs = model(data)
            _, predicted = outputs.max(1)
            
            all_predictions.extend(predicted.cpu().numpy())
            all_targets.extend(targets.cpu().numpy())
            
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()
    
    accuracy = 100. * correct / total
    logger.info(f"Test Accuracy: {accuracy:.2f}% ({correct}/{total})")
    
    return all_predictions, all_targets, accuracy

def generate_detailed_report(predictions, targets, vocab):
    """Generate detailed classification report and confusion matrix."""
    
    # Get label names
    label_to_word = vocab['label_to_word']
    class_names = [label_to_word[str(i)] for i in range(len(label_to_word))]
    
    # Classification report
    report = classification_report(
        targets, predictions, 
        target_names=class_names,
        output_dict=True,
        zero_division=0
    )
    
    # Print summary
    print("\n" + "="*60)
    print("DETAILED TEST RESULTS")
    print("="*60)
    print(f"Overall Accuracy: {report['accuracy']:.4f}")
    print(f"Macro Average Precision: {report['macro avg']['precision']:.4f}")
    print(f"Macro Average Recall: {report['macro avg']['recall']:.4f}")
    print(f"Macro Average F1-Score: {report['macro avg']['f1-score']:.4f}")
    
    # Top performing classes
    class_scores = []
    for class_name in class_names:
        if class_name in report:
            f1_score = report[class_name]['f1-score']
            class_scores.append((class_name, f1_score))
    
    class_scores.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\nTop 10 Best Performing Classes:")
    for i, (class_name, f1_score) in enumerate(class_scores[:10]):
        print(f"{i+1:2d}. {class_name:20s} - F1: {f1_score:.4f}")
    
    print(f"\nBottom 10 Performing Classes:")
    for i, (class_name, f1_score) in enumerate(class_scores[-10:]):
        print(f"{i+1:2d}. {class_name:20s} - F1: {f1_score:.4f}")
    
    return report

def plot_confusion_matrix(predictions, targets, vocab, save_path='models/confusion_matrix.png'):
    """Plot and save confusion matrix for top classes."""
    
    # Get top 20 most frequent classes for visualization
    unique_targets, counts = np.unique(targets, return_counts=True)
    top_indices = np.argsort(counts)[-20:]  # Top 20 most frequent
    
    # Filter predictions and targets for top classes
    mask = np.isin(targets, unique_targets[top_indices])
    filtered_targets = np.array(targets)[mask]
    filtered_predictions = np.array(predictions)[mask]
    
    # Create confusion matrix
    cm = confusion_matrix(filtered_targets, filtered_predictions, labels=unique_targets[top_indices])
    
    # Get class names
    label_to_word = vocab['label_to_word']
    class_names = [label_to_word[str(i)] for i in unique_targets[top_indices]]
    
    # Plot
    plt.figure(figsize=(15, 12))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Confusion Matrix - Top 20 Most Frequent Classes')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"Confusion matrix saved to {save_path}")

def main():
    """Main testing function."""

    # Check if model exists (prefer test model, fallback to validation model)
    test_model_path = Path('models/best_test_model.pth')
    val_model_path = Path('models/best_val_model.pth')

    if test_model_path.exists():
        model_path = test_model_path
        logger.info("Using best test model for evaluation")
    elif val_model_path.exists():
        model_path = val_model_path
        logger.info("Using best validation model for evaluation")
    else:
        logger.error("No trained model found. Please run training first!")
        return
    
    # Check if dataset exists
    metadata_path = Path('data/real_asl/WLASL/processed_metadata.json')
    if not metadata_path.exists():
        logger.error("Processed dataset not found. Please run data processing first!")
        return
    
    # Load model
    model, vocab = load_trained_model(model_path)
    if model is None:
        return
    
    # Create test dataset
    dataset = WLASLDataset(metadata_path, 'data/real_asl', augment=False)
    
    # Use same split as training (20% for validation/testing)
    val_size = int(len(dataset) * 0.2)
    train_size = len(dataset) - val_size
    _, test_dataset = random_split(dataset, [train_size, val_size])
    
    # Create test loader
    test_loader = DataLoader(
        test_dataset, 
        batch_size=8, 
        shuffle=False, 
        num_workers=2
    )
    
    logger.info(f"Testing on {len(test_dataset)} samples")
    
    # Test model
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    predictions, targets, accuracy = test_model(model, test_loader, vocab, device)
    
    # Generate detailed report
    report = generate_detailed_report(predictions, targets, vocab)
    
    # Plot confusion matrix
    plot_confusion_matrix(predictions, targets, vocab)
    
    # Save results
    results = {
        'test_accuracy': accuracy,
        'num_test_samples': len(test_dataset),
        'num_classes': vocab['num_classes'],
        'classification_report': report
    }
    
    with open('models/test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info("Test results saved to models/test_results.json")
    
    print("\n" + "="*60)
    print("TESTING COMPLETE!")
    print("="*60)
    print(f"✅ Test Accuracy: {accuracy:.2f}%")
    print(f"📊 Test Samples: {len(test_dataset)}")
    print(f"📊 Classes: {vocab['num_classes']}")
    print(f"💾 Results saved to: models/test_results.json")
    print(f"📈 Confusion matrix: models/confusion_matrix.png")
    print("="*60)

if __name__ == "__main__":
    main()
