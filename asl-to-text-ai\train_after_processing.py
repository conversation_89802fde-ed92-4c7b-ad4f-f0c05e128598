#!/usr/bin/env python3
"""
Training Script for ASL Model After Data Processing
Trains a neural network on the processed WLASL dataset created by robust_wlasl_pipeline.py
"""

import json
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
from pathlib import Path
import time
from tqdm import tqdm
import matplotlib.pyplot as plt
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WLASLDataset(Dataset):
    """Dataset class for processed WLASL data."""
    
    def __init__(self, metadata_path, data_root, augment=False):
        self.data_root = Path(data_root)
        self.augment = augment
        
        # Load metadata
        with open(metadata_path, 'r') as f:
            self.metadata = json.load(f)
        
        # Load vocabulary
        vocab_path = self.data_root / 'WLASL' / 'vocabulary.json'
        with open(vocab_path, 'r') as f:
            self.vocab = json.load(f)
        
        logger.info(f"Loaded dataset with {len(self.metadata)} samples")
        logger.info(f"Number of classes: {self.vocab['num_classes']}")
    
    def __len__(self):
        return len(self.metadata)
    
    def __getitem__(self, idx):
        item = self.metadata[idx]
        
        # Load frames
        frames_path = self.data_root / item['frames_path']
        frames = np.load(frames_path)  # Shape: (30, 224, 224, 3)
        
        # Convert to tensor and rearrange for PyTorch (T, C, H, W)
        frames = torch.FloatTensor(frames).permute(0, 3, 1, 2)
        
        # Apply simple augmentation during training if enabled
        if self.augment and torch.rand(1) > 0.5:
            # Random horizontal flip
            frames = torch.flip(frames, [3])
        
        label = item['label']
        
        return frames, label

class ASLVideoClassifier(nn.Module):
    """3D CNN for ASL video classification."""
    
    def __init__(self, num_classes, dropout_rate=0.5):
        super(ASLVideoClassifier, self).__init__()
        
        # 3D Convolutional layers
        self.conv3d1 = nn.Conv3d(3, 64, kernel_size=(3, 7, 7), stride=(1, 2, 2), padding=(1, 3, 3))
        self.bn1 = nn.BatchNorm3d(64)
        self.pool1 = nn.MaxPool3d(kernel_size=(1, 3, 3), stride=(1, 2, 2), padding=(0, 1, 1))
        
        self.conv3d2 = nn.Conv3d(64, 128, kernel_size=(3, 5, 5), stride=(1, 1, 1), padding=(1, 2, 2))
        self.bn2 = nn.BatchNorm3d(128)
        self.pool2 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        self.conv3d3 = nn.Conv3d(128, 256, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))
        self.bn3 = nn.BatchNorm3d(256)
        self.pool3 = nn.MaxPool3d(kernel_size=(2, 2, 2), stride=(2, 2, 2))
        
        self.conv3d4 = nn.Conv3d(256, 512, kernel_size=(3, 3, 3), stride=(1, 1, 1), padding=(1, 1, 1))
        self.bn4 = nn.BatchNorm3d(512)
        self.pool4 = nn.AdaptiveAvgPool3d((1, 7, 7))
        
        # Fully connected layers
        self.dropout = nn.Dropout(dropout_rate)
        self.fc1 = nn.Linear(512 * 7 * 7, 1024)
        self.fc2 = nn.Linear(1024, 512)
        self.fc3 = nn.Linear(512, num_classes)
        
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        # Input shape: (batch_size, 30, 3, 224, 224)
        # Rearrange to (batch_size, 3, 30, 224, 224) for 3D conv
        x = x.permute(0, 2, 1, 3, 4)
        
        x = self.relu(self.bn1(self.conv3d1(x)))
        x = self.pool1(x)
        
        x = self.relu(self.bn2(self.conv3d2(x)))
        x = self.pool2(x)
        
        x = self.relu(self.bn3(self.conv3d3(x)))
        x = self.pool3(x)
        
        x = self.relu(self.bn4(self.conv3d4(x)))
        x = self.pool4(x)
        
        # Flatten
        x = x.view(x.size(0), -1)
        
        # Fully connected layers
        x = self.dropout(self.relu(self.fc1(x)))
        x = self.dropout(self.relu(self.fc2(x)))
        x = self.fc3(x)
        
        return x

def evaluate_model(model, data_loader, criterion, device):
    """Evaluate model on given data loader."""
    model.eval()
    total_loss = 0.0
    correct = 0
    total = 0

    with torch.no_grad():
        for data, targets in data_loader:
            data, targets = data.to(device), targets.to(device)
            outputs = model(data)
            loss = criterion(outputs, targets)

            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()

    avg_loss = total_loss / len(data_loader)
    accuracy = 100. * correct / total

    return avg_loss, accuracy

def train_model():
    """Main training function."""

    # Configuration
    config = {
        'batch_size': 8,  # Reduced for memory efficiency
        'learning_rate': 1e-4,
        'num_epochs': 50,
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'save_every': 10,
        'test_every': 5,  # Test model every 5 epochs
        'val_split': 0.15,  # Reduced validation split to have more for testing
        'test_split': 0.15,  # Separate test set
        'dropout_rate': 0.5,
        'weight_decay': 1e-4,
        'early_stopping_patience': 10  # Stop if no improvement for 10 test evaluations
    }
    
    logger.info(f"Training configuration: {config}")
    logger.info(f"Using device: {config['device']}")
    
    # Check if processed dataset exists
    metadata_path = Path('data/real_asl/WLASL/processed_metadata.json')
    vocab_path = Path('data/real_asl/WLASL/vocabulary.json')
    
    if not metadata_path.exists():
        logger.error("Processed dataset not found. Run robust_wlasl_pipeline.py first!")
        return None, None
    
    if not vocab_path.exists():
        logger.error("Vocabulary file not found. Run robust_wlasl_pipeline.py first!")
        return None, None
    
    # Load vocabulary to get number of classes
    with open(vocab_path, 'r') as f:
        vocab = json.load(f)
    
    num_classes = vocab['num_classes']
    logger.info(f"Number of classes: {num_classes}")
    
    # Create dataset
    dataset = WLASLDataset(metadata_path, 'data/real_asl', augment=True)

    # Split dataset into train/val/test
    test_size = int(len(dataset) * config['test_split'])
    val_size = int(len(dataset) * config['val_split'])
    train_size = len(dataset) - val_size - test_size

    train_dataset, val_dataset, test_dataset = random_split(
        dataset, [train_size, val_size, test_size]
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=2,
        pin_memory=True if config['device'] == 'cuda' else False
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=2,
        pin_memory=True if config['device'] == 'cuda' else False
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=2,
        pin_memory=True if config['device'] == 'cuda' else False
    )

    logger.info(f"Train samples: {len(train_dataset)}, Val samples: {len(val_dataset)}, Test samples: {len(test_dataset)}")
    
    # Initialize model
    model = ASLVideoClassifier(num_classes=num_classes, dropout_rate=config['dropout_rate'])
    model = model.to(config['device'])
    
    # Loss and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(
        model.parameters(), 
        lr=config['learning_rate'], 
        weight_decay=config['weight_decay']
    )
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=config['num_epochs'])
    
    # Training loop
    best_test_acc = 0.0
    best_val_acc = 0.0
    train_losses = []
    val_accuracies = []
    test_accuracies = []
    epochs_without_improvement = 0

    logger.info("Starting training...")
    
    for epoch in range(config['num_epochs']):
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']} [Train]")
        
        for batch_idx, (data, targets) in enumerate(train_pbar):
            data, targets = data.to(config['device']), targets.to(config['device'])
            
            optimizer.zero_grad()
            outputs = model(data)
            loss = criterion(outputs, targets)
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = outputs.max(1)
            train_total += targets.size(0)
            train_correct += predicted.eq(targets).sum().item()
            
            # Update progress bar
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })
        
        # Validation phase
        val_loss, val_acc = evaluate_model(model, val_loader, criterion, config['device'])

        # Calculate metrics
        train_acc = 100. * train_correct / train_total
        avg_train_loss = train_loss / len(train_loader)

        train_losses.append(avg_train_loss)
        val_accuracies.append(val_acc)

        logger.info(f"Epoch {epoch+1}: Train Acc: {train_acc:.2f}%, Val Acc: {val_acc:.2f}%")

        # Periodic testing
        test_acc = None
        if (epoch + 1) % config['test_every'] == 0:
            logger.info(f"Running test evaluation at epoch {epoch+1}...")
            test_loss, test_acc = evaluate_model(model, test_loader, criterion, config['device'])
            test_accuracies.append((epoch+1, test_acc))
            logger.info(f"Test Accuracy: {test_acc:.2f}%")

            # Check if this is the best test performance
            if test_acc > best_test_acc:
                best_test_acc = test_acc
                epochs_without_improvement = 0

                # Save best model based on test performance
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'test_acc': test_acc,
                    'val_acc': val_acc,
                    'train_acc': train_acc,
                    'config': config,
                    'vocab': vocab
                }, 'models/best_test_model.pth')
                logger.info(f"🏆 NEW BEST TEST MODEL! Test Acc: {test_acc:.2f}% (saved to best_test_model.pth)")
            else:
                epochs_without_improvement += 1
                logger.info(f"No test improvement for {epochs_without_improvement} evaluations")

        # Early stopping based on test performance
        if epochs_without_improvement >= config['early_stopping_patience']:
            logger.info(f"Early stopping triggered after {epochs_without_improvement} evaluations without improvement")
            break
        
        # Update learning rate
        scheduler.step()

        # Save best validation model (separate from test model)
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc if test_acc is not None else 0.0,
                'config': config,
                'vocab': vocab
            }, 'models/best_val_model.pth')
            logger.info(f"📈 New best validation model saved: {val_acc:.2f}%")
        
        # Save checkpoint
        if (epoch + 1) % config['save_every'] == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'config': config,
                'vocab': vocab
            }, f'models/checkpoint_epoch_{epoch+1}.pth')
    
    logger.info(f"Training completed!")
    logger.info(f"Best validation accuracy: {best_val_acc:.2f}%")
    logger.info(f"Best test accuracy: {best_test_acc:.2f}%")

    # Run final test evaluation if not done recently
    if len(test_accuracies) == 0 or test_accuracies[-1][0] != config['num_epochs']:
        logger.info("Running final test evaluation...")
        final_test_loss, final_test_acc = evaluate_model(model, test_loader, criterion, config['device'])
        test_accuracies.append((config['num_epochs'], final_test_acc))
        logger.info(f"Final test accuracy: {final_test_acc:.2f}%")

    # Save final training results
    results = {
        'best_val_accuracy': best_val_acc,
        'best_test_accuracy': best_test_acc,
        'final_val_accuracy': val_accuracies[-1] if val_accuracies else 0.0,
        'final_test_accuracy': test_accuracies[-1][1] if test_accuracies else 0.0,
        'train_losses': train_losses,
        'val_accuracies': val_accuracies,
        'test_accuracies': test_accuracies,
        'config': config,
        'num_classes': num_classes,
        'total_samples': len(dataset),
        'train_samples': len(train_dataset),
        'val_samples': len(val_dataset),
        'test_samples': len(test_dataset)
    }
    
    with open('models/training_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    return model, results

if __name__ == "__main__":
    # Create models directory
    Path('models').mkdir(exist_ok=True)
    
    # Start training
    model, results = train_model()
    
    if model and results:
        print("\n" + "="*60)
        print("🎉 TRAINING COMPLETE!")
        print("="*60)
        print(f"✅ Best Validation Accuracy: {results['best_val_accuracy']:.2f}%")
        print(f"🏆 Best Test Accuracy: {results['best_test_accuracy']:.2f}%")
        print(f"📊 Classes: {results['num_classes']}")
        print(f"📊 Train/Val/Test Samples: {results['train_samples']}/{results['val_samples']}/{results['test_samples']}")
        print(f"💾 Best validation model: models/best_val_model.pth")
        print(f"🏆 Best test model: models/best_test_model.pth")
        print(f"📈 Training results: models/training_results.json")
        print("="*60)
        print("💡 Use best_test_model.pth for final deployment!")
        print("="*60)
    else:
        print("Training failed!")
