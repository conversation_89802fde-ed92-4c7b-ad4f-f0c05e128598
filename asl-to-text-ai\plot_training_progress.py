#!/usr/bin/env python3
"""
Plot Training Progress
Visualizes training and validation curves, plus test accuracy over time.
"""

import json
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

def plot_training_progress():
    """Plot training progress from saved results."""
    
    results_path = Path('models/training_results.json')
    if not results_path.exists():
        print("Training results not found. Please run training first!")
        return
    
    # Load results
    with open(results_path, 'r') as f:
        results = json.load(f)
    
    # Extract data
    train_losses = results['train_losses']
    val_accuracies = results['val_accuracies']
    test_accuracies = results.get('test_accuracies', [])
    
    # Create figure with subplots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('ASL Model Training Progress', fontsize=16, fontweight='bold')
    
    # Plot 1: Training Loss
    epochs = range(1, len(train_losses) + 1)
    ax1.plot(epochs, train_losses, 'b-', linewidth=2, label='Training Loss')
    ax1.set_title('Training Loss Over Time')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # Plot 2: Validation Accuracy
    ax2.plot(epochs, val_accuracies, 'g-', linewidth=2, label='Validation Accuracy')
    ax2.set_title('Validation Accuracy Over Time')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy (%)')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # Plot 3: Test Accuracy (if available)
    if test_accuracies:
        test_epochs, test_accs = zip(*test_accuracies)
        ax3.plot(test_epochs, test_accs, 'ro-', linewidth=2, markersize=8, label='Test Accuracy')
        ax3.set_title('Test Accuracy Over Time')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Accuracy (%)')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # Add best test accuracy annotation
        best_test_idx = np.argmax(test_accs)
        best_test_epoch = test_epochs[best_test_idx]
        best_test_acc = test_accs[best_test_idx]
        ax3.annotate(f'Best: {best_test_acc:.2f}%', 
                    xy=(best_test_epoch, best_test_acc),
                    xytext=(best_test_epoch + 2, best_test_acc + 2),
                    arrowprops=dict(arrowstyle='->', color='red'),
                    fontsize=10, fontweight='bold')
    else:
        ax3.text(0.5, 0.5, 'No test data available', 
                ha='center', va='center', transform=ax3.transAxes,
                fontsize=12, style='italic')
        ax3.set_title('Test Accuracy Over Time')
    
    # Plot 4: Combined Accuracy Comparison
    ax4.plot(epochs, val_accuracies, 'g-', linewidth=2, label='Validation Accuracy')
    if test_accuracies:
        test_epochs, test_accs = zip(*test_accuracies)
        ax4.plot(test_epochs, test_accs, 'ro-', linewidth=2, markersize=6, label='Test Accuracy')
    ax4.set_title('Validation vs Test Accuracy')
    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('Accuracy (%)')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    # Add summary statistics
    best_val_acc = results.get('best_val_accuracy', 0)
    best_test_acc = results.get('best_test_accuracy', 0)
    final_val_acc = results.get('final_val_accuracy', 0)
    final_test_acc = results.get('final_test_accuracy', 0)
    
    summary_text = f"""Training Summary:
Best Validation: {best_val_acc:.2f}%
Best Test: {best_test_acc:.2f}%
Final Validation: {final_val_acc:.2f}%
Final Test: {final_test_acc:.2f}%
Total Epochs: {len(train_losses)}
Classes: {results.get('num_classes', 'N/A')}"""
    
    fig.text(0.02, 0.02, summary_text, fontsize=10, 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)
    
    # Save plot
    plt.savefig('models/training_progress.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Training progress plot saved to: models/training_progress.png")

def print_summary():
    """Print a text summary of training results."""
    
    results_path = Path('models/training_results.json')
    if not results_path.exists():
        print("Training results not found.")
        return
    
    with open(results_path, 'r') as f:
        results = json.load(f)
    
    print("\n" + "="*60)
    print("TRAINING SUMMARY")
    print("="*60)
    print(f"📊 Dataset Info:")
    print(f"   Total Samples: {results.get('total_samples', 'N/A')}")
    print(f"   Train/Val/Test: {results.get('train_samples', 'N/A')}/{results.get('val_samples', 'N/A')}/{results.get('test_samples', 'N/A')}")
    print(f"   Classes: {results.get('num_classes', 'N/A')}")
    
    print(f"\n🎯 Performance:")
    print(f"   Best Validation Accuracy: {results.get('best_val_accuracy', 0):.2f}%")
    print(f"   Best Test Accuracy: {results.get('best_test_accuracy', 0):.2f}%")
    print(f"   Final Validation Accuracy: {results.get('final_val_accuracy', 0):.2f}%")
    print(f"   Final Test Accuracy: {results.get('final_test_accuracy', 0):.2f}%")
    
    print(f"\n⚙️ Training Config:")
    config = results.get('config', {})
    print(f"   Epochs Completed: {len(results.get('train_losses', []))}")
    print(f"   Batch Size: {config.get('batch_size', 'N/A')}")
    print(f"   Learning Rate: {config.get('learning_rate', 'N/A')}")
    print(f"   Test Every: {config.get('test_every', 'N/A')} epochs")
    
    test_accuracies = results.get('test_accuracies', [])
    if test_accuracies:
        print(f"\n📈 Test Evaluations: {len(test_accuracies)} times")
        for epoch, acc in test_accuracies:
            print(f"   Epoch {epoch}: {acc:.2f}%")
    
    print("="*60)

if __name__ == "__main__":
    print("ASL Training Progress Visualization")
    print("="*40)
    
    # Print text summary
    print_summary()
    
    # Plot graphs
    print("\nGenerating training progress plots...")
    plot_training_progress()
