@echo off
echo ========================================
echo ASL Complete Pipeline: Processing + Training
echo ========================================
echo.

echo Step 1: Installing required packages...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install opencv-python numpy tqdm matplotlib requests urllib3 kaggle

echo.
echo Step 2: Running data processing pipeline...
python robust_wlasl_pipeline.py

echo.
echo Checking if processing was successful...
if not exist "data\real_asl\WLASL\processed_metadata.json" (
    echo ERROR: Data processing failed! processed_metadata.json not found.
    echo Please check the processing step and try again.
    pause
    exit /b 1
)

if not exist "data\real_asl\WLASL\vocabulary.json" (
    echo ERROR: Data processing failed! vocabulary.json not found.
    echo Please check the processing step and try again.
    pause
    exit /b 1
)

echo ✅ Data processing completed successfully!

echo.
echo Step 3: Starting model training...
python train_after_processing.py

echo.
echo ========================================
echo Pipeline Complete!
echo ========================================
echo.
echo Check the following files:
echo - models/best_asl_model.pth (best trained model)
echo - models/training_results.json (training metrics)
echo - data/real_asl/WLASL/processed_metadata.json (processed dataset)
echo.
pause
